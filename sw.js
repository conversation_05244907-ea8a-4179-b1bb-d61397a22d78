// Simple Service Worker for Tetris Game
const CACHE_NAME = 'tetris-v202506171440';
const urlsToCache = [
    '/',
    '/index.html',
    '/styles/main.css',
    '/js/main.js',
    '/js/GameEngine.js',
    '/js/Tetromino.js',
    '/js/AudioManager.js',
    '/js/Renderer.js',
    '/js/InputHandler.js',
    '/js/utils.js'
];

// Install event - cache resources
self.addEventListener('install', (event) => {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then((cache) => {
                // console.log('Opened cache');
                return cache.addAll(urlsToCache);
            })
    );
});

// Fetch event - serve from cache when offline
self.addEventListener('fetch', (event) => {
    event.respondWith(
        caches.match(event.request)
            .then((response) => {
                // Return cached version or fetch from network
                return response || fetch(event.request);
            }
        )
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
    event.waitUntil(
        caches.keys().then((cacheNames) => {
            return Promise.all(
                cacheNames.map((cacheName) => {
                    if (cacheName !== CACHE_NAME) {
                        console.log('Deleting old cache:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
});
